import { motion } from 'framer-motion'

const Academics = () => {
  return (
    <div className="pt-20">
      <section className="py-20 hero-gradient text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">Academics</h1>
            <p className="text-xl opacity-90">Excellence in Education</p>
          </motion.div>
        </div>
      </section>
      <section className="py-20">
        <div className="container mx-auto px-4">
          <p className="text-center text-muted-foreground">Academic programs and curriculum details coming soon...</p>
        </div>
      </section>
    </div>
  )
}

export default Academics

