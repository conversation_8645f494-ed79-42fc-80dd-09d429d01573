import{toNestErrors as e,validateFieldsNatively as t}from"@hookform/resolvers";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n.apply(null,arguments)}function r(e){if(e.path?.length){let t="";for(const n of e.path){const e="object"==typeof n?n.key:n;if("string"!=typeof e&&"number"!=typeof e)return null;t+=t?`.${e}`:e}return t}return null}function s(s,o,a={}){return async(o,i,l)=>{let u=s["~standard"].validate(o);if(u instanceof Promise&&(u=await u),u.issues){const t=function(e,t){const s={};for(let o=0;o<e.length;o++){const a=e[o],i=r(a);if(i&&(s[i]||(s[i]={message:a.message,type:""}),t)){const e=s[i].types||{};s[i].types=n({},e,{[Object.keys(e).length]:a.message})}}return s}(u.issues,!l.shouldUseNativeValidation&&"all"===l.criteriaMode);return{values:{},errors:e(t,l)}}return l.shouldUseNativeValidation&&t({},l),{values:a.raw?Object.assign({},o):u.value,errors:{}}}}export{s as arktypeResolver};
//# sourceMappingURL=arktype.modern.mjs.map
