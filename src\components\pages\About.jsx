import { motion } from 'framer-motion'
import { Award, Users, Globe, BookOpen, Target, Eye, Heart } from 'lucide-react'
import { Card, CardContent } from '../ui/card'

const About = () => {
  const values = [
    {
      icon: Target,
      title: 'Excellence',
      description: 'We strive for the highest standards in everything we do, from academics to character development.'
    },
    {
      icon: Heart,
      title: 'Compassion',
      description: 'We foster a caring community where every individual is valued and supported.'
    },
    {
      icon: Globe,
      title: 'Global Perspective',
      description: 'We prepare students to be responsible global citizens in an interconnected world.'
    },
    {
      icon: BookOpen,
      title: 'Innovation',
      description: 'We embrace new ideas and technologies to enhance learning and teaching.'
    }
  ]

  const milestones = [
    { year: '2010', event: 'SN Global Academy Founded', description: 'Established with a vision to transform education' },
    { year: '2015', event: 'International Accreditation', description: 'Received global recognition for academic excellence' },
    { year: '2018', event: 'Technology Integration', description: 'Launched comprehensive digital learning platform' },
    { year: '2020', event: 'Global Expansion', description: 'Extended reach to students worldwide through online programs' },
    { year: '2023', event: 'Innovation Center', description: 'Opened state-of-the-art research and innovation facility' },
    { year: '2024', event: 'Sustainability Initiative', description: 'Launched comprehensive environmental sustainability program' }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 hero-gradient text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              About SN Global Academy
            </h1>
            <p className="text-xl opacity-90 leading-relaxed">
              Transforming education through innovation, creativity, and global collaboration 
              to prepare students for success in an ever-changing world.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="flex items-center space-x-3 mb-6">
                <Eye className="h-8 w-8 text-primary" />
                <h2 className="text-3xl font-bold text-gradient">Our Vision</h2>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed">
                To establish SN Global Academy as a leader in transformative, global education 
                that inspires innovation, fosters inclusivity, and prepares students to become 
                responsible global citizens who can positively impact the world.
              </p>
              <Card className="p-6 card-3d bg-primary/5">
                <CardContent className="p-0">
                  <h3 className="text-xl font-semibold mb-3 text-primary">Vision Goals</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                      Global educational leadership
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                      Innovation in teaching and learning
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                      Inclusive and diverse community
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="flex items-center space-x-3 mb-6">
                <Target className="h-8 w-8 text-secondary" />
                <h2 className="text-3xl font-bold text-gradient">Our Mission</h2>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Our mission is to provide exceptional, innovative education that develops each 
                student intellectually, artistically, emotionally, physically, and socially, 
                preparing them for success in college, career, and life as engaged global citizens.
              </p>
              <Card className="p-6 card-3d bg-secondary/5">
                <CardContent className="p-0">
                  <h3 className="text-xl font-semibold mb-3 text-secondary">Mission Focus</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-secondary rounded-full mr-3"></div>
                      Holistic student development
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-secondary rounded-full mr-3"></div>
                      College and career readiness
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-secondary rounded-full mr-3"></div>
                      Global citizenship preparation
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gradient">
              Our Core Values
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              These fundamental principles guide everything we do and shape the character 
              of our educational community.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="card-3d"
              >
                <Card className="p-6 h-full text-center">
                  <CardContent className="p-0">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <value.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                    <p className="text-muted-foreground">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* History & Milestones */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gradient">
              Our Journey
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From our founding to today, we've continuously evolved to meet the changing 
              needs of education and prepare students for the future.
            </p>
          </motion.div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20 hidden lg:block"></div>

            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className={`flex flex-col lg:flex-row items-center gap-8 ${
                    index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                  }`}
                >
                  <div className="lg:w-1/2">
                    <Card className="p-6 card-3d">
                      <CardContent className="p-0">
                        <div className="flex items-center mb-3">
                          <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4">
                            {milestone.year.slice(-2)}
                          </div>
                          <div>
                            <h3 className="text-xl font-semibold">{milestone.event}</h3>
                            <p className="text-primary font-medium">{milestone.year}</p>
                          </div>
                        </div>
                        <p className="text-muted-foreground">{milestone.description}</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Timeline Dot */}
                  <div className="hidden lg:block w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg"></div>

                  <div className="lg:w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Accreditations */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gradient">
              Accreditations & Recognition
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12">
              Our commitment to excellence is recognized by leading educational organizations worldwide.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <Award className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">International Baccalaureate</h3>
                  <p className="text-muted-foreground">Authorized IB World School</p>
                </CardContent>
              </Card>

              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <Globe className="h-12 w-12 text-secondary mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Global Education Network</h3>
                  <p className="text-muted-foreground">Member of prestigious education consortium</p>
                </CardContent>
              </Card>

              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <Users className="h-12 w-12 text-accent mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Educational Excellence</h3>
                  <p className="text-muted-foreground">Certified for outstanding academic standards</p>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default About

