var e=require("@hookform/resolvers");function r(){return r=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},r.apply(null,arguments)}function t(e){if(e.path?.length){let r="";for(const t of e.path){const e="object"==typeof t?t.key:t;if("string"!=typeof e&&"number"!=typeof e)return null;r+=r?`.${e}`:e}return r}return null}exports.arktypeResolver=function(n,s,o){return void 0===o&&(o={}),function(s,a,i){try{var u=function(){if(l.issues){var n=function(e,n){for(var s={},o=0;o<e.length;o++){var a=e[o],i=t(a);if(i&&(s[i]||(s[i]={message:a.message,type:""}),n)){var u,l=s[i].types||{};s[i].types=r({},l,((u={})[Object.keys(l).length]=a.message,u))}}return s}(l.issues,!i.shouldUseNativeValidation&&"all"===i.criteriaMode);return{values:{},errors:e.toNestErrors(n,i)}}return i.shouldUseNativeValidation&&e.validateFieldsNatively({},i),{values:o.raw?Object.assign({},s):l.value,errors:{}}},l=n["~standard"].validate(s),f=function(){if(l instanceof Promise)return Promise.resolve(l).then(function(e){l=e})}();return Promise.resolve(f&&f.then?f.then(u):u())}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=arktype.js.map
