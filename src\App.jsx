import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import './App.css'

// Components
import Header from './components/Header'
import Footer from './components/Footer'

// Pages
import Home from './components/pages/Home'
import About from './components/pages/About'
import Management from './components/pages/Management'
import Academics from './components/pages/Academics'
import Admissions from './components/pages/Admissions'
import StudentLife from './components/pages/StudentLife'
import Parents from './components/pages/Parents'
import Alumni from './components/pages/Alumni'
import News from './components/pages/News'
import Careers from './components/pages/Careers'
import Contact from './components/pages/Contact'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-background">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/management" element={<Management />} />
            <Route path="/academics" element={<Academics />} />
            <Route path="/admissions" element={<Admissions />} />
            <Route path="/student-life" element={<StudentLife />} />
            <Route path="/parents" element={<Parents />} />
            <Route path="/alumni" element={<Alumni />} />
            <Route path="/news" element={<News />} />
            <Route path="/careers" element={<Careers />} />
            <Route path="/contact" element={<Contact />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  )
}

export default App

