var e=require("@hookform/resolvers"),r=require("effect"),t=require("effect/ParseResult"),o=require("react-hook-form");exports.effectTsResolver=function(n,a){return void 0===a&&(a={errors:"all",onExcessProperty:"ignore"}),function(s,f,i){return t.decodeUnknown(n,a)(s).pipe(r.Effect.catchAll(function(e){return r.Effect.flip(t.ArrayFormatter.formatIssue(e))}),r.Effect.mapError(function(r){var t=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,n=r.reduce(function(e,r){var n=r.path.join(".");if(e[n]||(e[n]={message:r.message,type:r._tag}),t){var a=e[n].types,s=a&&a[String(r._tag)];e[n]=o.appendErrors(n,t,e,r._tag,s?[].concat(s,r.message):r.message)}return e},{});return e.toNestErrors(n,i)}),r.Effect.tap(function(){return r.Effect.sync(function(){return i.shouldUseNativeValidation&&e.validateFieldsNatively({},i)})}),r.Effect.match({onFailure:function(e){return{errors:e,values:{}}},onSuccess:function(e){return{errors:{},values:e}}}),r.Effect.runPromise)}};
//# sourceMappingURL=effect-ts.js.map
