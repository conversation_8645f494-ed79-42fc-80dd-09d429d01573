import { motion } from 'framer-motion'
import { ArrowRight, Play, Users, BookOpen, Award, Globe, Calendar, ChevronRight } from 'lucide-react'
import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import heroImage from '../../assets/homepage-hero-clean.jpg'
import studentsLearning from '../../assets/students-learning.jpg'
import classroomActivities from '../../assets/classroom-activities.jpg'
import interactiveLearning from '../../assets/interactive-learning.jpg'

const Home = () => {
  const stats = [
    { icon: Users, value: '5,000+', label: 'Students' },
    { icon: BookOpen, value: '200+', label: 'Courses' },
    { icon: Award, value: '50+', label: 'Awards' },
    { icon: Globe, value: '25+', label: 'Countries' },
  ]

  const features = [
    {
      title: 'Innovative Curriculum',
      description: 'Cutting-edge programs designed for the future of learning',
      image: classroomActivities,
      color: 'from-blue-500 to-purple-600'
    },
    {
      title: 'Global Community',
      description: 'Connect with students and educators from around the world',
      image: studentsLearning,
      color: 'from-green-500 to-teal-600'
    },
    {
      title: 'Interactive Learning',
      description: 'Hands-on experiences that bring education to life',
      image: interactiveLearning,
      color: 'from-orange-500 to-red-600'
    },
  ]

  const quickLinks = [
    { title: 'Student Portal', description: 'Access your courses and grades', path: '/student-portal' },
    { title: 'Parent Portal', description: 'Stay connected with your child\'s progress', path: '/parent-portal' },
    { title: 'Admissions', description: 'Start your journey with us', path: '/admissions' },
    { title: 'Calendar', description: 'View upcoming events and deadlines', path: '/calendar' },
  ]

  const news = [
    {
      title: 'New STEM Lab Opens',
      date: 'March 15, 2024',
      excerpt: 'State-of-the-art science and technology laboratory now available for all students.',
    },
    {
      title: 'International Exchange Program',
      date: 'March 10, 2024',
      excerpt: 'Students can now participate in our global exchange program with partner schools.',
    },
    {
      title: 'Academic Excellence Awards',
      date: 'March 5, 2024',
      excerpt: 'Celebrating our students\' outstanding achievements in academics and leadership.',
    },
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 hero-gradient opacity-90"></div>
        <div className="absolute inset-0">
          <img
            src={heroImage}
            alt="Students learning"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="max-w-4xl mx-auto"
          >
            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-6 animate-float"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 1.2, type: "spring" }}
            >
              Welcome to{' '}
              <span className="text-gradient bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                SN Global Academy
              </span>
            </motion.h1>
            
            <motion.p
              className="text-xl md:text-2xl mb-8 opacity-90"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 1 }}
            >
              Transforming education through innovation, creativity, and global collaboration
            </motion.p>
            
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
            >
              <Button size="lg" className="btn-3d bg-secondary hover:bg-secondary/90 text-lg px-8 py-4">
                Apply Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="btn-3d text-white border-white hover:bg-white hover:text-primary text-lg px-8 py-4">
                <Play className="mr-2 h-5 w-5" />
                Watch Video
              </Button>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2"></div>
          </div>
        </motion.div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="card-3d bg-card p-6 rounded-xl">
                  <stat.icon className="h-12 w-12 mx-auto mb-4 text-primary" />
                  <h3 className="text-3xl font-bold text-primary mb-2">{stat.value}</h3>
                  <p className="text-muted-foreground">{stat.label}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-8 text-gradient"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              Our Mission & Vision
            </motion.h2>
            <motion.p
              className="text-xl text-muted-foreground mb-12 leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
            >
              To establish SN Global Academy as a leader in transformative, global education 
              that prepares every student for success in college, career, and life through 
              innovative, inclusive, and inspiring learning experiences.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-center mb-16 text-gradient"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            Why Choose SN Global Academy?
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.8 }}
                viewport={{ once: true }}
                className="card-3d"
              >
                <Card className="overflow-hidden h-full">
                  <div className="relative h-48">
                    <img
                      src={feature.image}
                      alt={feature.title}
                      className="w-full h-full object-cover"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-t ${feature.color} opacity-80`}></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <h3 className="text-2xl font-bold text-white text-center px-4">
                        {feature.title}
                      </h3>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-center mb-16 text-gradient"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            Quick Access
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickLinks.map((link, index) => (
              <motion.div
                key={link.title}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
                className="card-3d"
              >
                <Card className="p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group">
                  <CardContent className="p-0">
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                      {link.title}
                    </h3>
                    <p className="text-muted-foreground text-sm mb-4">{link.description}</p>
                    <ChevronRight className="h-5 w-5 text-primary group-hover:translate-x-1 transition-transform" />
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Latest News */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <motion.h2
              className="text-4xl md:text-5xl font-bold text-gradient"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              Latest News
            </motion.h2>
            <Button variant="outline" className="btn-3d">
              View All News
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {news.map((article, index) => (
              <motion.div
                key={article.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="card-3d"
              >
                <Card className="p-6 hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-0">
                    <div className="flex items-center mb-3">
                      <Calendar className="h-4 w-4 text-primary mr-2" />
                      <span className="text-sm text-muted-foreground">{article.date}</span>
                    </div>
                    <h3 className="text-xl font-semibold mb-3 hover:text-primary transition-colors cursor-pointer">
                      {article.title}
                    </h3>
                    <p className="text-muted-foreground">{article.excerpt}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 hero-gradient text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of students who are already transforming their future with SN Global Academy
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="btn-3d text-lg px-8 py-4">
                Apply for Admission
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="btn-3d text-white border-white hover:bg-white hover:text-primary text-lg px-8 py-4">
                Schedule a Visit
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Home

