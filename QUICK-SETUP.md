# SN Global Academy - Quick Setup Guide

## 🚀 Quick Start (5 Minutes)

### 1. Prerequisites
- Install Node.js (v18+): https://nodejs.org/
- Install pnpm: `npm install -g pnpm`

### 2. Setup
```bash
# Extract the zip file
unzip sn-global-academy-source-code.zip
cd sn-global-academy-source

# Install dependencies
pnpm install

# Start development server
pnpm run dev
```

### 3. Open Browser
- Go to: http://localhost:5173
- The website will automatically reload when you make changes

## 📁 Key Files to Know

- **`src/components/pages/Management.jsx`** - Update team members here
- **`src/components/Header.jsx`** - Navigation menu
- **`src/assets/`** - Add images here
- **`src/App.css`** - Custom styles and animations

## 🔧 Common Tasks

### Add New Team Member
1. Add photo to `src/assets/`
2. Import in `Management.jsx`
3. Add to `managementTeam` array

### Change Colors
Edit `tailwind.config.js` or `src/App.css`

### Add New Page
1. Create component in `src/components/pages/`
2. Add route in `src/App.jsx`
3. Add to navigation in `Header.jsx`

## 🏗 Build & Deploy

### Build for Production
```bash
pnpm run build
```

### Deploy Options
- **Netlify**: Connect Git repo, auto-deploy
- **Vercel**: Import project, one-click deploy
- **Traditional Hosting**: Upload `dist` folder contents

## 📚 Full Documentation
See `developer-documentation.pdf` for complete guide including:
- Detailed setup instructions
- Customization guide
- Performance optimization
- Troubleshooting
- Maintenance procedures

## 🌐 Current Live Site
https://msszccgc.manus.space

---
**Need Help?** Check the full documentation or common troubleshooting section.

