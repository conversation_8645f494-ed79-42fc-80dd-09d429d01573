import { motion } from 'framer-motion'
import { Mail, Linkedin, Award, GraduationCap } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import shruthiPhoto from '../../assets/shruthi.jpg'
import nivikaPhoto from '../../assets/nivika.jpg'
import krupashiniPhoto from '../../assets/krupashini.jpg'
import nithyashreePhoto from '../../assets/nithyashree.jpg'

const Management = () => {
  const managementTeam = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      position: 'Chairman',
      image: nithyashreePhoto,
      bio: '<PERSON><PERSON><PERSON><PERSON> serves as the Chairman of SN Global Academy, bringing visionary leadership and strategic direction to the institution. With extensive experience in educational governance and institutional development, she has been instrumental in establishing the academy\'s core values and long-term vision. Her commitment to educational excellence and innovation has positioned SN Global Academy as a leader in transformative education. <PERSON><PERSON><PERSON><PERSON> believes in empowering educators and students to reach their full potential through collaborative leadership.',
      education: 'M.A. Educational Leadership & Governance',
      experience: '20+ years in Educational Leadership',
      achievements: ['Educational Visionary Award 2024', 'Institution Building Excellence'],
      email: '<EMAIL>',
      linkedin: '#'
    },
    {
      name: 'Shruthi',
      position: 'Managing Director',
      image: shruthiPhoto,
      bio: 'Shruthi brings over 15 years of experience in educational leadership and strategic planning. With a Master\'s in Educational Administration from Harvard University, she has been instrumental in transforming educational institutions across the globe. Her vision for SN Global Academy focuses on creating an inclusive, innovative learning environment that prepares students for the challenges of tomorrow. Shruthi is passionate about leveraging technology to enhance learning outcomes and believes in the power of education to change lives.',
      education: 'M.Ed. Educational Administration, Harvard University',
      experience: '15+ years in Educational Leadership',
      achievements: ['Educational Excellence Award 2023', 'Global Education Leader Recognition'],
      email: '<EMAIL>',
      linkedin: '#'
    },
    {
      name: 'Nivika',
      position: 'Chief Executive Officer',
      image: nivikaPhoto,
      bio: 'Nivika is a dynamic leader with a strong background in business administration and educational innovation. Holding an MBA from Stanford Graduate School of Business, she has successfully led multiple educational initiatives that have impacted thousands of students worldwide. Her strategic approach to education combines business acumen with pedagogical excellence. Nivika is committed to ensuring that SN Global Academy remains at the forefront of educational innovation while maintaining the highest standards of academic excellence.',
      education: 'MBA, Stanford Graduate School of Business',
      experience: '12+ years in Executive Leadership',
      achievements: ['CEO of the Year 2022', 'Innovation in Education Award'],
      email: '<EMAIL>',
      linkedin: '#'
    },
    {
      name: 'Krupashini',
      position: 'Principal',
      image: krupashiniPhoto,
      bio: 'Krupashini is an accomplished educator with over 18 years of experience in academic leadership and curriculum development. She holds a Ph.D. in Educational Psychology from Oxford University and has been recognized for her innovative teaching methodologies. As Principal, she oversees the academic programs and ensures that every student receives personalized attention to reach their full potential. Krupashini is passionate about creating a nurturing environment where students can explore their interests and develop critical thinking skills.',
      education: 'Ph.D. Educational Psychology, Oxford University',
      experience: '18+ years in Academic Leadership',
      achievements: ['Outstanding Principal Award 2023', 'Curriculum Innovation Excellence'],
      email: '<EMAIL>',
      linkedin: '#'
    }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 hero-gradient text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Our Leadership Team
            </h1>
            <p className="text-xl opacity-90 leading-relaxed">
              Meet the visionary leaders who are driving SN Global Academy's mission to 
              transform education and inspire the next generation of global citizens.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Leadership Philosophy */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-gradient">
              Leadership Philosophy
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Our leadership team believes in collaborative governance, innovative thinking, 
              and student-centered decision making. Together, they bring decades of experience 
              in education, administration, and strategic planning to ensure that SN Global 
              Academy continues to set new standards in global education.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Management Team */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="space-y-16">
            {managementTeam.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.8 }}
                viewport={{ once: true }}
                className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-12 items-center`}
              >
                {/* Image */}
                <div className="lg:w-1/3">
                  <motion.div
                    className="card-3d"
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="relative overflow-hidden rounded-2xl">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-96 object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-primary/80 to-transparent"></div>
                      <div className="absolute bottom-6 left-6 text-white">
                        <h3 className="text-2xl font-bold">{member.name}</h3>
                        <p className="text-lg opacity-90">{member.position}</p>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Content */}
                <div className="lg:w-2/3 space-y-6">
                  <div>
                    <h3 className="text-3xl font-bold text-gradient mb-2">{member.name}</h3>
                    <p className="text-xl text-primary font-semibold">{member.position}</p>
                  </div>

                  <p className="text-muted-foreground leading-relaxed text-lg">
                    {member.bio}
                  </p>

                  {/* Credentials */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="p-4">
                      <CardContent className="p-0">
                        <div className="flex items-center mb-2">
                          <GraduationCap className="h-5 w-5 text-primary mr-2" />
                          <span className="font-semibold">Education</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{member.education}</p>
                      </CardContent>
                    </Card>

                    <Card className="p-4">
                      <CardContent className="p-0">
                        <div className="flex items-center mb-2">
                          <Award className="h-5 w-5 text-primary mr-2" />
                          <span className="font-semibold">Experience</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{member.experience}</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Achievements */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Award className="h-5 w-5 text-primary mr-2" />
                      Key Achievements
                    </h4>
                    <ul className="space-y-2">
                      {member.achievements.map((achievement, i) => (
                        <li key={i} className="text-muted-foreground flex items-center">
                          <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Contact */}
                  <div className="flex space-x-4">
                    <Button variant="outline" size="sm" className="btn-3d">
                      <Mail className="h-4 w-4 mr-2" />
                      Contact
                    </Button>
                    <Button variant="outline" size="sm" className="btn-3d">
                      <Linkedin className="h-4 w-4 mr-2" />
                      LinkedIn
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Values */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-gradient">
              Our Leadership Values
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">Excellence</h3>
                  <p className="text-muted-foreground">
                    Committed to the highest standards in education and administration
                  </p>
                </CardContent>
              </Card>

              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <GraduationCap className="h-8 w-8 text-secondary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">Innovation</h3>
                  <p className="text-muted-foreground">
                    Embracing new ideas and technologies to enhance learning
                  </p>
                </CardContent>
              </Card>

              <Card className="p-6 card-3d">
                <CardContent className="p-0 text-center">
                  <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Mail className="h-8 w-8 text-accent" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">Collaboration</h3>
                  <p className="text-muted-foreground">
                    Working together to create the best outcomes for our students
                  </p>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Management

