#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/parser/bin/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/parser/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/parser/bin/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/parser/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules/@babel/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/@babel+parser@7.27.2/node_modules:/mnt/d/Sampath/Sampath/Learn/LLMagent learn/Manus/sn-global-academy-source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/babel-parser.js" "$@"
else
  exec node  "$basedir/../../bin/babel-parser.js" "$@"
fi
