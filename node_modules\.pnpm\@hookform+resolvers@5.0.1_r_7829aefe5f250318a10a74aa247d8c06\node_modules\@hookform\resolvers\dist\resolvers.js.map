{"version": 3, "file": "resolvers.js", "sources": ["../src/validateFieldsNatively.ts", "../src/toNestErrors.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field && field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => {\n  const path = escapeBrackets(name);\n  return names.some((n) => escapeBrackets(n).match(`^${path}\\\\.\\\\d+`));\n};\n\n/**\n * Escapes special characters in a string to be used in a regex pattern.\n * it removes the brackets from the string to match the `set` method.\n *\n * @param input - The input string to escape.\n * @returns The escaped string.\n */\nfunction escapeBrackets(input: string): string {\n  return input.replace(/\\]|\\[/g, '');\n}\n"], "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "_loop", "field", "fields", "refs", "for<PERSON>ach", "isNameInFieldArray", "names", "name", "path", "escapeBrackets", "some", "n", "match", "input", "replace", "shouldUseNativeValidation", "fieldErrors", "Object", "assign", "keys", "fieldArrayErrors", "set"], "mappings": "iCASMA,EAAoB,SACxBC,EACAC,EACAC,GAEA,GAAIF,GAAO,mBAAoBA,EAAK,CAClC,IAAMG,EAAQC,MAAIF,EAAQD,GAC1BD,EAAID,kBAAmBI,GAASA,EAAME,SAAY,IAElDL,EAAIM,gBACN,CACF,EAGaC,EAAyB,SACpCL,EACAM,GACQC,IAAAA,WAAAR,GAEN,IAAMS,EAAQF,EAAQG,OAAOV,GACzBS,GAASA,EAAMV,KAAO,mBAAoBU,EAAMV,IAClDD,EAAkBW,EAAMV,IAAKC,EAAWC,GAC/BQ,GAASA,EAAME,MACxBF,EAAME,KAAKC,QAAQ,SAACb,UAClBD,EAAkBC,EAAKC,EAAWC,EAAO,EAG/C,EATA,IAAK,IAAMD,KAAaO,EAAQG,OAAMF,EAAAR,EAUxC,ECAMa,EAAqB,SACzBC,EACAC,GAEA,IAAMC,EAAOC,EAAeF,GAC5B,OAAOD,EAAMI,KAAK,SAACC,GAAM,OAAAF,EAAeE,GAAGC,MAAK,IAAKJ,EAAI,UAAU,EACrE,EASA,SAASC,EAAeI,GACtB,OAAOA,EAAMC,QAAQ,SAAU,GACjC,sBA3C4B,SAC1BrB,EACAM,GAEAA,EAAQgB,2BAA6BjB,EAAuBL,EAAQM,GAEpE,IAAMiB,EAAc,CAA+B,EACnD,IAAK,IAAMR,KAAQf,EAAQ,CACzB,IAAMQ,EAAQN,EAAGA,IAACI,EAAQG,OAAQM,GAC5Bd,EAAQuB,OAAOC,OAAOzB,EAAOe,IAAS,GAAI,CAC9CjB,IAAKU,GAASA,EAAMV,MAGtB,GAAIc,EAAmBN,EAAQO,OAASW,OAAOE,KAAK1B,GAASe,GAAO,CAClE,IAAMY,EAAmBH,OAAOC,OAAO,CAAE,EAAEvB,EAAGA,IAACqB,EAAaR,IAE5Da,MAAID,EAAkB,OAAQ1B,GAC9B2B,MAAIL,EAAaR,EAAMY,EACzB,MACEC,EAAGA,IAACL,EAAaR,EAAMd,EAE3B,CAEA,OAAOsB,CACT"}