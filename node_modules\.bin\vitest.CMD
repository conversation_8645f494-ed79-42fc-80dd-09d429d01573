@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\vitest@2.1.9_jsdom@26.1.0_lightningcss@1.30.1\node_modules\vitest\node_modules;D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\vitest@2.1.9_jsdom@26.1.0_lightningcss@1.30.1\node_modules;D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\vitest@2.1.9_jsdom@26.1.0_lightningcss@1.30.1\node_modules\vitest\node_modules;D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\vitest@2.1.9_jsdom@26.1.0_lightningcss@1.30.1\node_modules;D:\Sampath\Sampath\Learn\LLMagent learn\Manus\sn-global-academy-source\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
