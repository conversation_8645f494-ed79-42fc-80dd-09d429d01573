{"version": 3, "file": "protocol.js", "sourceRoot": "", "sources": ["../../../src/shared/protocol.ts"], "names": [], "mappings": "AACA,OAAO,EACL,2BAA2B,EAE3B,SAAS,EACT,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EACjB,qBAAqB,EAKrB,QAAQ,EAER,iBAAiB,EAGjB,0BAA0B,GAM3B,MAAM,aAAa,CAAC;AAuBrB;;GAEG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,KAAK,CAAC;AA0GlD;;;GAGG;AACH,MAAM,OAAgB,QAAQ;IAmD5B,YAAoB,QAA0B;QAA1B,aAAQ,GAAR,QAAQ,CAAkB;QA7CtC,sBAAiB,GAAG,CAAC,CAAC;QACtB,qBAAgB,GAMpB,IAAI,GAAG,EAAE,CAAC;QACN,oCAA+B,GACrC,IAAI,GAAG,EAAE,CAAC;QACJ,0BAAqB,GAGzB,IAAI,GAAG,EAAE,CAAC;QACN,sBAAiB,GAGrB,IAAI,GAAG,EAAE,CAAC;QACN,sBAAiB,GAAkC,IAAI,GAAG,EAAE,CAAC;QAC7D,iBAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;QA2BzD,IAAI,CAAC,sBAAsB,CAAC,2BAA2B,EAAE,CAAC,YAAY,EAAE,EAAE;YACxE,MAAM,UAAU,GAAG,IAAI,CAAC,+BAA+B,CAAC,GAAG,CACzD,YAAY,CAAC,MAAM,CAAC,SAAS,CAC9B,CAAC;YACF,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,EAAE,CAAC,YAAY,EAAE,EAAE;YACvE,IAAI,CAAC,WAAW,CAAC,YAA+C,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CACpB,iBAAiB;QACjB,6BAA6B;QAC7B,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAgB,CAClC,CAAC;IACJ,CAAC;IAEO,aAAa,CACnB,SAAiB,EACjB,OAAe,EACf,eAAmC,EACnC,SAAqB,EACrB,yBAAkC,KAAK;QAEvC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE;YAC/B,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC;YACzC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;YACP,eAAe;YACf,sBAAsB;YACtB,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,SAAiB;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QACjD,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACjE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpC,MAAM,IAAI,QAAQ,CAChB,SAAS,CAAC,cAAc,EACxB,gCAAgC,EAChC,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE,CACxD,CAAC;QACJ,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,SAAiB;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CAAC,SAAoB;QAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;YACzC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAC7C,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAClC,CAAC;iBAAM,IAAI,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAEO,QAAQ;;QACd,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,MAAA,IAAI,CAAC,OAAO,oDAAI,CAAC;QAEjB,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;QAC5E,KAAK,MAAM,OAAO,IAAI,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,QAAQ,CAAC,KAAY;;QAC3B,MAAA,IAAI,CAAC,OAAO,qDAAG,KAAK,CAAC,CAAC;IACxB,CAAC;IAEO,eAAe,CAAC,YAAiC;;QACvD,MAAM,OAAO,GACX,MAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,mCACnD,IAAI,CAAC,2BAA2B,CAAC;QAEnC,gDAAgD;QAChD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,sFAAsF;QACtF,OAAO,CAAC,OAAO,EAAE;aACd,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;aACjC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CACf,IAAI,CAAC,QAAQ,CACX,IAAI,KAAK,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAC9D,CACF,CAAC;IACN,CAAC;IAEO,UAAU,CAAC,OAAuB,EAAE,KAA+B;;QACzE,MAAM,OAAO,GACX,MAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,mCAAI,IAAI,CAAC,sBAAsB,CAAC;QAE3E,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,MAAA,IAAI,CAAC,UAAU,0CACX,IAAI,CAAC;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,SAAS,CAAC,cAAc;oBAC9B,OAAO,EAAE,kBAAkB;iBAC5B;aACF,EACA,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CACf,IAAI,CAAC,QAAQ,CACX,IAAI,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CACxD,CACF,CAAC;YACJ,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAEtE,MAAM,SAAS,GAAyD;YACtE,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,SAAS,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,SAAS;YACrC,KAAK,EAAE,MAAA,OAAO,CAAC,MAAM,0CAAE,KAAK;YAC5B,gBAAgB,EACd,CAAC,YAAY,EAAE,EAAE,CACf,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;YACrE,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,OAAQ,EAAE,EAAE,CACzC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;YAC7E,QAAQ,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ;YACzB,SAAS,EAAE,OAAO,CAAC,EAAE;SACtB,CAAC;QAEF,sFAAsF;QACtF,OAAO,CAAC,OAAO,EAAE;aACd,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aACvC,IAAI,CACH,CAAC,MAAM,EAAE,EAAE;;YACT,IAAI,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,OAAO,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,CAAC;gBAC3B,MAAM;gBACN,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;aACf,CAAC,CAAC;QACL,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;;YACR,IAAI,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,OAAO,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,CAAC;gBAC3B,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBACvC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;wBACf,CAAC,CAAC,SAAS,CAAC,aAAa;oBAC3B,OAAO,EAAE,MAAA,KAAK,CAAC,OAAO,mCAAI,gBAAgB;iBAC3C;aACF,CAAC,CAAC;QACL,CAAC,CACF;aACA,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CACf,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC,CAC9D;aACA,OAAO,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,WAAW,CAAC,YAAkC;QACpD,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC;QACzD,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;QAExC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,0DAA0D,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;YACnH,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAErD,IAAI,WAAW,IAAI,eAAe,IAAI,WAAW,CAAC,sBAAsB,EAAE,CAAC;YACzE,IAAI,CAAC;gBACH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAe,CAAC,KAAc,CAAC,CAAC;gBAChC,OAAO;YACT,CAAC;QACH,CAAC;QAED,OAAO,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAEO,WAAW,CAAC,QAAwC;QAC1D,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CACX,IAAI,KAAK,CACP,kDAAkD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAC7E,CACF,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAEhC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,QAAQ,CACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,EACnB,QAAQ,CAAC,KAAK,CAAC,OAAO,EACtB,QAAQ,CAAC,KAAK,CAAC,IAAI,CACpB,CAAC;YACF,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;;QACT,MAAM,CAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,KAAK,EAAE,CAAA,CAAC;IACjC,CAAC;IA2BD;;;;OAIG;IACH,OAAO,CACL,OAAqB,EACrB,YAAe,EACf,OAAwB;QAExB,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QAE/E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;YACrC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,IAAI,CAAA,MAAA,IAAI,CAAC,QAAQ,0CAAE,yBAAyB,MAAK,IAAI,EAAE,CAAC;gBACtD,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,CAAC;YAED,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,cAAc,EAAE,CAAC;YAElC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,cAAc,GAAmB;gBACrC,GAAG,OAAO;gBACV,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,SAAS;aACd,CAAC;YAEF,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE,CAAC;gBACxB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC1D,cAAc,CAAC,MAAM,GAAG;oBACtB,GAAG,OAAO,CAAC,MAAM;oBACjB,KAAK,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE;iBACpC,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,CAAC,MAAe,EAAE,EAAE;;gBACjC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAEhC,MAAA,IAAI,CAAC,UAAU,0CACX,IAAI,CAAC;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,yBAAyB;oBACjC,MAAM,EAAE;wBACN,SAAS,EAAE,SAAS;wBACpB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;qBACvB;iBACF,EAAE,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,EAC1D,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CACf,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC,CAClE,CAAC;gBAEJ,MAAM,CAAC,MAAM,CAAC,CAAC;YACjB,CAAC,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE;;gBACjD,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,OAAO,EAAE,CAAC;oBAC7B,OAAO;gBACT,CAAC;gBAED,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;oBAC9B,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC1B,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBACnD,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;;gBAC9C,MAAM,CAAC,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,MAAM,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,mCAAI,4BAA4B,CAAC;YACjE,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,QAAQ,CAC9C,SAAS,CAAC,cAAc,EACxB,mBAAmB,EACnB,EAAE,OAAO,EAAE,CACZ,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,EAAE,cAAc,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,mCAAI,KAAK,CAAC,CAAC;YAE3H,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC7G,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAChC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,YAA+B,EAAE,OAA6B;QAC/E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEvD,MAAM,mBAAmB,GAAwB;YAC/C,GAAG,YAAY;YACf,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAKf,aAAgB,EAChB,OAGuC;QAEvC,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAE5C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACnD,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,MAAc;QACjC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,MAAc;QACvC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CACb,yBAAyB,MAAM,4CAA4C,CAC5E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CAKpB,kBAAqB,EACrB,OAA2D;QAE3D,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAC5B,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EACrC,CAAC,YAAY,EAAE,EAAE,CACf,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CACnE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,MAAc;QACtC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;CACF;AAED,MAAM,UAAU,iBAAiB,CAE/B,IAAO,EAAE,UAAa;IACtB,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CACtC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACpB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAE,GAAG,IAAI,EAAE,CACZ,CAAC;AACJ,CAAC"}