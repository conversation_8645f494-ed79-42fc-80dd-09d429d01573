# SN Global Academy Website - Developer Documentation

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Development Setup](#development-setup)
4. [Project Structure](#project-structure)
5. [Development Workflow](#development-workflow)
6. [Building & Deployment](#building--deployment)
7. [Customization Guide](#customization-guide)
8. [Maintenance & Updates](#maintenance--updates)
9. [Troubleshooting](#troubleshooting)
10. [Performance Optimization](#performance-optimization)

---

## 🎯 Project Overview

**SN Global Academy Website** is a modern, responsive educational institution website built with React 18 and Vite. The website features:

- **Modern Design**: Clean, professional UI with 3D effects and animations
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Performance Optimized**: Fast loading times with optimized assets
- **SEO Ready**: Proper meta tags and semantic HTML structure
- **Accessibility**: WCAG compliant design patterns

### Key Features
- ✅ Multi-page navigation with dropdown menus
- ✅ Management team profiles with photos
- ✅ Responsive design for all devices
- ✅ Smooth animations and transitions
- ✅ Contact forms and interactive elements
- ✅ Professional branding and styling

---

## 🛠 Technology Stack

### Frontend Framework
- **React 18.3.1** - Modern React with hooks and functional components
- **Vite 6.3.5** - Fast build tool and development server
- **React Router DOM 7.1.1** - Client-side routing

### Styling & UI
- **Tailwind CSS 3.4.17** - Utility-first CSS framework
- **Framer Motion 11.15.0** - Animation library
- **Lucide React 0.468.0** - Icon library
- **Radix UI** - Accessible UI components

### Development Tools
- **ESLint** - Code linting
- **PostCSS** - CSS processing
- **Autoprefixer** - CSS vendor prefixes

---

## 🚀 Development Setup

### Prerequisites
Ensure you have the following installed:
- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **pnpm** (recommended) or npm/yarn
- **Git** for version control
- **Cursor IDE** or VS Code

### Installation Steps

1. **Extract the source code**
   ```bash
   # Extract the provided zip file
   unzip sn-global-academy-source-code.zip
   cd sn-global-academy-source
   ```

2. **Install dependencies**
   ```bash
   # Using pnpm (recommended)
   pnpm install
   
   # Or using npm
   npm install
   
   # Or using yarn
   yarn install
   ```

3. **Start development server**
   ```bash
   # Using pnpm
   pnpm run dev
   
   # Or using npm
   npm run dev
   
   # Or using yarn
   yarn dev
   ```

4. **Open in browser**
   - Navigate to `http://localhost:5173`
   - The site will automatically reload when you make changes

### Cursor IDE Setup

1. **Open project in Cursor**
   ```bash
   cursor sn-global-academy-source
   ```

2. **Install recommended extensions**
   - ES7+ React/Redux/React-Native snippets
   - Tailwind CSS IntelliSense
   - Auto Rename Tag
   - Prettier - Code formatter
   - ESLint

3. **Configure workspace settings**
   Create `.vscode/settings.json`:
   ```json
   {
     "editor.formatOnSave": true,
     "editor.defaultFormatter": "esbenp.prettier-vscode",
     "tailwindCSS.experimental.classRegex": [
       ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
       ["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([a-zA-Z0-9\\-:]+)"]
     ]
   }
   ```

---

## 📁 Project Structure

```
sn-global-academy-source/
├── public/                     # Static assets
├── src/
│   ├── assets/                 # Images, logos, media files
│   │   ├── logo.png           # Main logo
│   │   ├── shruthi.jpg        # Management team photos
│   │   ├── nivika.jpg
│   │   ├── krupashini.jpg
│   │   ├── nithyashree.jpg
│   │   └── ...
│   ├── components/            # React components
│   │   ├── pages/             # Page components
│   │   │   ├── Home.jsx       # Homepage
│   │   │   ├── About.jsx      # About page
│   │   │   ├── Management.jsx # Management team
│   │   │   └── ...
│   │   ├── ui/                # Reusable UI components
│   │   ├── Header.jsx         # Navigation header
│   │   └── Footer.jsx         # Site footer
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility functions
│   ├── App.jsx               # Main app component
│   ├── App.css               # Global styles
│   ├── main.jsx              # App entry point
│   └── index.css             # Tailwind imports
├── package.json              # Dependencies and scripts
├── vite.config.js           # Vite configuration
├── tailwind.config.js       # Tailwind configuration
└── README.md                # Project documentation
```

### Key Files Explained

- **`src/App.jsx`** - Main application with routing setup
- **`src/components/Header.jsx`** - Navigation with dropdown menus
- **`src/components/pages/Management.jsx`** - Management team profiles
- **`src/App.css`** - Custom CSS and 3D effects
- **`vite.config.js`** - Build configuration
- **`package.json`** - Project dependencies and scripts

---

## 💻 Development Workflow

### Available Scripts

```bash
# Development
pnpm run dev          # Start development server
pnpm run build        # Build for production
pnpm run preview      # Preview production build
pnpm run lint         # Run ESLint

# Package management
pnpm install          # Install dependencies
pnpm add <package>    # Add new dependency
pnpm remove <package> # Remove dependency
```

### Development Best Practices

1. **Component Structure**
   - Use functional components with hooks
   - Keep components small and focused
   - Use proper prop types and default values

2. **Styling Guidelines**
   - Use Tailwind CSS classes for styling
   - Custom CSS only for complex animations
   - Follow mobile-first responsive design

3. **Code Organization**
   - Group related components in folders
   - Use descriptive file and variable names
   - Keep imports organized and clean

4. **Performance**
   - Optimize images before adding to assets
   - Use lazy loading for heavy components
   - Minimize bundle size with tree shaking

---

## 🏗 Building & Deployment

### Production Build

```bash
# Create production build
pnpm run build

# The build files will be in the 'dist' folder
# These files are ready for deployment
```

### Deployment Options

#### 1. **Netlify (Recommended)**
1. Create account at [netlify.com](https://netlify.com)
2. Connect your Git repository
3. Set build command: `pnpm run build`
4. Set publish directory: `dist`
5. Deploy automatically on git push

#### 2. **Vercel**
1. Create account at [vercel.com](https://vercel.com)
2. Import your Git repository
3. Vercel auto-detects Vite projects
4. Deploy with one click

#### 3. **GitHub Pages**
1. Install gh-pages: `pnpm add -D gh-pages`
2. Add to package.json:
   ```json
   {
     "homepage": "https://yourusername.github.io/repository-name",
     "scripts": {
       "predeploy": "pnpm run build",
       "deploy": "gh-pages -d dist"
     }
   }
   ```
3. Run: `pnpm run deploy`

#### 4. **Traditional Web Hosting**
1. Build the project: `pnpm run build`
2. Upload the `dist` folder contents to your web server
3. Configure server to serve `index.html` for all routes

### Environment Variables

Create `.env` file for environment-specific settings:
```env
VITE_API_URL=https://api.snglobalacademy.edu
VITE_CONTACT_EMAIL=<EMAIL>
VITE_ANALYTICS_ID=your-analytics-id
```

Access in code:
```javascript
const apiUrl = import.meta.env.VITE_API_URL;
```

---

## 🎨 Customization Guide

### Updating Content

#### 1. **Management Team**
Edit `src/components/pages/Management.jsx`:
```javascript
const managementTeam = [
  {
    name: 'New Person',
    position: 'New Position',
    image: newPersonPhoto, // Import the image
    bio: 'Biography text...',
    education: 'Education details',
    experience: 'Experience details',
    achievements: ['Achievement 1', 'Achievement 2'],
    email: '<EMAIL>',
    linkedin: '#'
  }
];
```

#### 2. **Adding New Pages**
1. Create new component in `src/components/pages/`
2. Add route in `src/App.jsx`:
   ```javascript
   <Route path="/new-page" element={<NewPage />} />
   ```
3. Add to navigation in `src/components/Header.jsx`

#### 3. **Updating Images**
1. Add images to `src/assets/`
2. Import in component:
   ```javascript
   import newImage from '../assets/new-image.jpg';
   ```
3. Use in JSX:
   ```javascript
   <img src={newImage} alt="Description" />
   ```

### Styling Customization

#### 1. **Colors**
Edit `tailwind.config.js`:
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        }
      }
    }
  }
}
```

#### 2. **Fonts**
Add to `src/index.css`:
```css
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
  font-family: 'Inter', sans-serif;
}
```

#### 3. **Custom Animations**
Add to `src/App.css`:
```css
.custom-animation {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
```

### Adding New Features

#### 1. **Contact Form**
```javascript
import { useState } from 'react';

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input
        type="text"
        placeholder="Name"
        value={formData.name}
        onChange={(e) => setFormData({...formData, name: e.target.value})}
        className="w-full p-3 border rounded-lg"
      />
      {/* Add more fields */}
      <button type="submit" className="bg-primary text-white px-6 py-3 rounded-lg">
        Submit
      </button>
    </form>
  );
};
```

#### 2. **Blog/News Section**
1. Create `src/components/pages/Blog.jsx`
2. Add blog data structure
3. Implement blog listing and detail views
4. Add to navigation

---

## 🔧 Maintenance & Updates

### Regular Maintenance Tasks

#### 1. **Dependency Updates**
```bash
# Check for outdated packages
pnpm outdated

# Update all dependencies
pnpm update

# Update specific package
pnpm add package-name@latest
```

#### 2. **Security Updates**
```bash
# Audit for vulnerabilities
pnpm audit

# Fix vulnerabilities
pnpm audit --fix
```

#### 3. **Performance Monitoring**
- Use Lighthouse for performance audits
- Monitor Core Web Vitals
- Optimize images and assets regularly

### Content Updates

#### 1. **Adding New Team Members**
1. Add photo to `src/assets/`
2. Update `Management.jsx` with new member data
3. Test on all devices
4. Deploy changes

#### 2. **Updating Contact Information**
1. Update in `Footer.jsx`
2. Update in `Contact.jsx`
3. Update environment variables if needed

#### 3. **News and Events**
1. Create news component if not exists
2. Add news data structure
3. Implement CRUD operations
4. Consider using a CMS for easier updates

### Backup Strategy

1. **Code Backup**
   - Use Git for version control
   - Push to remote repository regularly
   - Tag releases for easy rollback

2. **Asset Backup**
   - Keep original high-resolution images
   - Backup to cloud storage
   - Document asset sources

---

## 🐛 Troubleshooting

### Common Issues

#### 1. **Build Errors**
```bash
# Clear cache and reinstall
rm -rf node_modules pnpm-lock.yaml
pnpm install

# Clear Vite cache
rm -rf .vite
pnpm run dev
```

#### 2. **Image Loading Issues**
- Ensure images are in `src/assets/`
- Check import paths are correct
- Verify image file extensions match imports

#### 3. **Routing Issues**
- Check React Router setup in `App.jsx`
- Ensure all routes have corresponding components
- Configure server for SPA routing

#### 4. **Styling Issues**
- Check Tailwind CSS is properly configured
- Verify class names are correct
- Use browser dev tools to debug styles

### Performance Issues

#### 1. **Slow Loading**
- Optimize images (use WebP format)
- Implement lazy loading
- Minimize bundle size

#### 2. **Memory Leaks**
- Clean up event listeners in useEffect
- Avoid creating objects in render
- Use React DevTools Profiler

### Browser Compatibility

#### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

#### Polyfills
Add to `vite.config.js` if needed:
```javascript
export default {
  build: {
    target: 'es2015'
  }
}
```

---

## ⚡ Performance Optimization

### Image Optimization

#### 1. **Image Formats**
- Use WebP for modern browsers
- Provide JPEG fallbacks
- Optimize file sizes (aim for <500KB)

#### 2. **Lazy Loading**
```javascript
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./LazyComponent'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

### Code Splitting

#### 1. **Route-based Splitting**
```javascript
import { lazy } from 'react';

const Home = lazy(() => import('./pages/Home'));
const About = lazy(() => import('./pages/About'));
```

#### 2. **Component Splitting**
```javascript
// Split heavy components
const HeavyChart = lazy(() => import('./HeavyChart'));
```

### Bundle Optimization

#### 1. **Analyze Bundle**
```bash
# Install bundle analyzer
pnpm add -D rollup-plugin-visualizer

# Add to vite.config.js
import { visualizer } from 'rollup-plugin-visualizer';

export default {
  plugins: [
    visualizer({
      filename: 'dist/stats.html',
      open: true
    })
  ]
}
```

#### 2. **Tree Shaking**
- Import only what you need
- Use ES6 modules
- Avoid importing entire libraries

### Caching Strategy

#### 1. **Browser Caching**
Configure in deployment:
```
# .htaccess for Apache
<IfModule mod_expires.c>
  ExpiresActive on
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
</IfModule>
```

#### 2. **Service Worker**
Consider adding PWA features:
```bash
pnpm add -D vite-plugin-pwa
```

---

## 📞 Support & Resources

### Documentation Links
- [React Documentation](https://react.dev/)
- [Vite Documentation](https://vitejs.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [Framer Motion Documentation](https://www.framer.com/motion/)

### Development Tools
- [React DevTools](https://chrome.google.com/webstore/detail/react-developer-tools/)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)

### Community Resources
- [React Community](https://reactjs.org/community/support.html)
- [Tailwind CSS Discord](https://discord.gg/7NF8GNe)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/reactjs)

---

## 📝 Changelog

### Version 1.0.0 (Current)
- ✅ Initial website development
- ✅ Responsive design implementation
- ✅ Management team integration
- ✅ Navigation and routing setup
- ✅ Performance optimization
- ✅ Mobile navigation fixes

### Future Enhancements
- [ ] Content Management System integration
- [ ] Blog/News section
- [ ] Student portal integration
- [ ] Online application system
- [ ] Multi-language support
- [ ] Advanced SEO optimization

---

**Happy Coding! 🚀**

For any questions or support, refer to this documentation or reach out to the development team.

